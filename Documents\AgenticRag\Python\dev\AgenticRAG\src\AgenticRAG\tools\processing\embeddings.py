# embedding_generate.py
from imports import *
from etc.helper_functions import *

from llama_index.core import (
    VectorStoreIndex,
    SimpleDirectoryReader,
    StorageContext,
    load_index_from_storage,
)
from qdrant_client.http import models
from llama_index.vector_stores.qdrant import QdrantVectorStore
from managers.manager_qdrant import Q<PERSON>rant<PERSON>anager
from managers.manager_postgreSQL import PostgreSQLManager

from llama_index.embeddings.fastembed import FastEmbedEmbedding
from llama_index.vector_stores.qdrant import QdrantVectorStore
from llama_index.core import VectorStoreIndex

# class BGEEmbeddings(BaseEmbedding):
#     def __init__(self):
#         super().__init__()
#         model_name = model_name = OptimumEmbedding(folder_name=str(BASE_DIR() / "bge_onnx"))
#         #model_name = FastEmbedEmbedding(model_name="BAAI/bge-base-en-v1.5")
#         BGEEmbeddings.model_name = model_name

#     def _get_text_embedding(self, text: str) -> List[float]:
#         """Generate embedding for a single text string."""
#         return BGEEmbeddings.model_name.get_text_embedding(text)

#     def _get_query_embedding(self, query: str) -> List[float]:
#         """Generate embedding for a query (same as text embedding here)."""
#         return BGEEmbeddings.model_name.get_text_embedding(query)

#     def _get_text_embeddings(self, texts: List[str]) -> List[List[float]]:
#         """Generate embeddings for a list of texts."""
#         return [BGEEmbeddings.model_name.get_text_embedding(text) for text in texts]

#     async def _aget_text_embedding(self, text: str) -> List[float]:
#         """Async version of text embedding (optional, for async support)."""
#         return BGEEmbeddings._get_text_embedding(text)

#     async def _aget_query_embedding(self, query: str) -> List[float]:
#         """Async version of query embedding."""
#         return BGEEmbeddings._get_query_embedding(query)






async def generateEmbedding(DATA_DIR, PERSIST_DIR, parsers):
    #bge_embedding = BGEEmbeddings()
    print("Generating index. This may take a WHILE!")
    if etc.helper_functions.folder_has_files(DATA_DIR):
        documents = SimpleDirectoryReader(DATA_DIR,recursive=True, file_extractor=parsers.file_extractor).load_data()

    if Globals.is_docker() == False:
        await PostgreSQLManager.delete_database("meltanodb")
        await PostgreSQLManager.create_database("meltanodb")
        from subprocess import run as subprocess_run
        result = subprocess_run('cmd /c playwright install', shell=True, capture_output=True, text=True)
    await QDrantManager.GetAsyncClient().delete_collection(collection_name="mainCollection")
    try:
        await QDrantManager.GetAsyncClient().create_collection(
            collection_name="mainCollection",
            vectors_config={
                "text-dense": models.VectorParams( # We kunnen "dense" gebruiken als naam voor de dichte vector
                    size=EMBEDDING_SIZE,
                    distance=models.Distance.COSINE,
                )
            },
            sparse_vectors_config={
                "text-sparse": models.SparseVectorParams( # We gebruiken "sparse" als naam voor de sparse vector, wat de standaard output_name is
                    index=models.SparseIndexParams(
                        on_disk=False,
                    )
                )
            },
            hnsw_config=models.HnswConfigDiff(m=16, ef_construct=100)
        )
    except Exception as e:
        # Does cause an error on the server since we use client AND aclient, but doesn't actually cause a problem
        # @Simon: The error itself is a warning from what I can tell? An actual error would stop the create_collection trigger?
        etc.helper_functions.exception_triggered(e)

    # Setup the ONNX embedding model


    # Setup the sparse BM25 embedding model
    #sparse_embed_model = FastEmbedEmbedding(model_name="Qdrant/bm25")

    embed_model = ZairaSettings.OllamaSettings().embed_model

    vector_store = QdrantVectorStore(client=QDrantManager.GetClient(), aclient=QDrantManager.GetAsyncClient(), collection_name="mainCollection",enable_hybrid=True,sparse_encoder=ZairaSettings.sparse_embed_model,batch_size=20,vector_field_name="dense",sparse_vector_field_name="sparse")
    storage_context = StorageContext.from_defaults(vector_store=vector_store)
    # Create index from documents using the ONNX embedding model
    index = VectorStoreIndex.from_documents(
        documents,
        storage_context=storage_context,
        embed_model=embed_model,
        vector_store=vector_store,
        vector_field_name="dense"
    )
    #index = VectorStoreIndex([], storage_context=storage_context, embed_model=ZairaSettings.OllamaSettings().embed_model)
    index.set_index_id(index_id="mainIndex")
    # Manually embed each document before adding to ChromaDB
    #new_nodes = []
    #if helper_functions.folder_has_files(DATA_DIR):
    #    for i, doc in enumerate(documents):
    #        text = str(doc.text) if doc.text is not None else ""
    #        embedding = bge_embedding([text])
    #        embedding = embedding[0]  # Ensure this returns the correct format for Chroma
    #        new_nodes.append(TextNode(text=text, id_=f"new-id-{i}", metadata=doc.metadata, embedding=embedding))
    #vector_store.add(new_nodes)  # Or use async_add if you're dealing with asynchronous operations



    index.storage_context.persist(persist_dir=str(PERSIST_DIR))

    print("Index saved to SSD")
    return index

async def loadEmbedding(PERSIST_DIR):
    #bge_embedding = BGEEmbeddings()
    print("Loading stored index")

    vector_store = QdrantVectorStore(client=QDrantManager.GetClient(), aclient=QDrantManager.GetAsyncClient(), collection_name="mainCollection",enable_hybrid=True,sparse_encoder=ZairaSettings.sparse_embed_model,batch_size=20)

    # Create a fresh storage context that only uses the vector store
    # Don't load from persisted docstore/index_store as they may be outdated after crawling
    storage_context = StorageContext.from_defaults(vector_store=vector_store)

    # Try to load existing index, but if it fails, create a new one from the vector store
    try:
        index = load_index_from_storage(index_id="mainIndex", storage_context=storage_context)
        print("Index loaded from storage")
    except Exception as e:
        print(f"Could not load index from storage ({e}), creating new index from vector store")
        # Create a new index from the existing vector store data
        index = VectorStoreIndex.from_vector_store(
            vector_store=vector_store,
            storage_context=storage_context,
            embed_model=ZairaSettings.OllamaSettings().embed_model
        )
        index.set_index_id(index_id="mainIndex")

    # Persist the updated storage context
    try:
        index.storage_context.persist(persist_dir=str(PERSIST_DIR))
        print("Storage context persisted")
    except Exception as e:
        print(f"Warning: Could not persist storage context: {e}")

    #Check for any additional files that need to be added to the database
    #reader = SimpleDirectoryReader(DATA_DIR,recursive=True, file_extractor=parsers.file_extractor)
    #new_docs = reader.load_data()
    #new_nodes = []
    #for i, doc in enumerate(new_docs):
    #    embedding = bge_embedding([doc.text])[0]  # Ensure this returns the correct format for Chroma
    #    new_nodes.append(TextNode(text=doc.text, id_=f"new-id-{i}", metadata=doc.metadata, embedding=embedding))
    #vector_store.add(new_nodes)  # Or use async_add if you're dealing with asynchronous operations
    #vector_store.persist(persist_dir=str(PERSIST_DIR))
    print("Index loaded")
    return index

async def some_llm_call(message):
    print("Calling LLM with:", message)
    answer = await some_llm_call(message)
    print("LLM returned:", answer)
    return answer
